import React, { useState, useEffect } from 'react';
import { Table, message, Spin } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { getRequest } from '../../utils/apiHandler';
import './BlendsPredictionTable.css';

interface BlendData {
  datetime: string;
  batch_id: string;
  L_color_value: number;
  R_color_value: number;
  C_color_value: number;
  moist: number;
  ffa: number;
  iv: number;
  ph: number;
  fat: number;
  cluster: string;
  quality: number;
}

interface BlendsPredictionTableProps {
  refreshInterval?: number; // in milliseconds, default 10000 (10 seconds)
  rowCount?: number; // number of random rows to fetch, default 5
}

const BlendsPredictionTable: React.FC<BlendsPredictionTableProps> = ({
  refreshInterval = 10000,
  rowCount = 1
}) => {
  const [data, setData] = useState<BlendData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Define table columns
  const columns: ColumnsType<BlendData> = [
    {
      title: 'Datetime',
      dataIndex: 'datetime',
      key: 'datetime',
      width: 150,
      fixed: 'left',
      render: (text: string) => (
        <span className="text-sm">{text}</span>
      ),
    },
    {
      title: 'Batch ID',
      dataIndex: 'batch_id',
      key: 'batch_id',
      width: 120,
      fixed: 'left',
      render: (text: string) => (
        <span className="font-medium text-sm">{text}</span>
      ),
    },
    {
      title: 'Prediction',
      children: [
        {
          title: 'L_color_value',
          dataIndex: 'L_color_value',
          key: 'L_color_value',
          width: 120,
          render: (value: number) => (
            <span className="text-sm">{value?.toFixed(3) || '0.000'}</span>
          ),
        },
        {
          title: 'H_color_Value',
          dataIndex: 'R_color_value',
          key: 'R_color_value',
          width: 120,
          render: (value: number) => (
            <span className="text-sm">{value?.toFixed(3) || '0.000'}</span>
          ),
        },
        {
          title: 'C_color_value',
          dataIndex: 'C_color_value',
          key: 'C_color_value',
          width: 120,
          render: (value: number) => (
            <span className="text-sm">{value?.toFixed(3) || '0.000'}</span>
          ),
        },
      ],
    },
  ];

  // Function to fetch random blend data
  const fetchBlendData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('rowCount', rowCount)
      const response = await getRequest(`/blends/random?count=${rowCount}`);
      
      if (response?.data?.data) {
        const newData = response.data.data.map((item: BlendData, index: number) => ({
          ...item,
          key: `${item.batch_id}_${Date.now()}_${index}`, // Unique key for each row
          datetime: new Date().toLocaleString(), // or use any format you prefer
        }));
        
        setData(prevData => {
          // Keep only the last 20 rows to prevent infinite growth
          const combinedData = [...prevData, ...newData];
          return combinedData.slice(-20);
        });
      } else {
        setError('Failed to fetch blend data');
        message.error('Failed to fetch blend data');
      }
    } catch (error) {
      console.error('Error fetching blend data:', error);
      setError('Error fetching blend data');
      message.error('Error fetching blend data');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchBlendData();
  }, [rowCount]);

  // Set up interval for auto-refresh
  useEffect(() => {
    const interval = setInterval(() => {
      fetchBlendData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, rowCount]);

  return (
    <div className="blends-prediction-table">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-2xl font-bold mb-2">Prediction Data</h3>
        <div className="flex items-center gap-2">
          {loading && <Spin size="small" />}
          <span className="text-sm text-gray-500">
            Auto-refresh every {refreshInterval / 1000}s
          </span>
        </div>
      </div>
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      <div className="overflow-x-auto">
        <Table
          columns={columns}
          dataSource={data}
          pagination={false}
          scroll={{ x: 800, y: 400 }}
          size="small"
          bordered
          className="blends-table"
          loading={loading && data.length === 0}
          locale={{
            emptyText: loading ? 'Loading...' : 'No data available'
          }}
        />
      </div>

      <div className="mt-2 text-xs text-gray-500">
        Showing latest {data.length} records • Data updates automatically
      </div>
    </div>
  );
};

export default BlendsPredictionTable;
