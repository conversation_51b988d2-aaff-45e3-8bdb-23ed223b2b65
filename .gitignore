# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
Backend/node_modules
/node_modules
Frontend/node_modules
node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
Frontend/build
node_modules
# misc
.env
Backend/.env
Frontend/.env
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
package.json
package-lock.json